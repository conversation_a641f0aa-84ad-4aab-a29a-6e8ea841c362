'use client';
import React, { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from './SidebarNav';
import {
	FaBars,
	FaChevronLeft,
	FaSignOutAlt,
	FaHome,
	FaBook,
	FaCalendarDay,
	FaTasks,
	FaUsers,
	FaUser,
	FaBell,
	FaBalanceScale,
} from 'react-icons/fa';
import { MdAdminPanelSettings, MdEventAvailable } from 'react-icons/md';
import { LuGoal } from 'react-icons/lu';
import { GiLaurels } from 'react-icons/gi';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Brain, Notebook } from 'lucide-react';
import SignOutConfirmModal from '../ui/SignOutConfirmModal';
import { useUserProfile } from '@/src/lib/hooks';

const navLinks = [
	{ icon: <FaHome className='w-6 h-6' />, label: 'Home', href: '/' },
	{ icon: <FaBook className='w-6 h-6' />, label: 'Courses', href: '/lessons' },
	{
		icon: <FaCalendarDay className='w-6 h-6' />,
		label: 'Review',
		href: '#',
		hasDropdown: true,
		subItems: [
			{
				icon: <Notebook className='w-4 h-4' />,
				label: 'Diary',
				href: '/diary',
			},
			{
				icon: <Brain className='w-4 h-4' />,
				label: 'Reflection',
				href: '/reflection',
			},
		],
	},
	{
		icon: <FaTasks className='w-6 h-6' />,
		href: '/life-mission',
		label: 'Life Mission',
		hasDropdown: true,
		subItems: [
			{
				icon: <FaBalanceScale className='w-4 h-4' />,
				label: 'Values',
				href: '/values',
			},
			{ icon: <LuGoal className='w-4 h-4' />, label: 'Goals', href: '/goals' },
			{
				icon: <GiLaurels className='w-4 h-4' />,
				label: 'Legacy',
				href: '/legacy',
			},
		],
	},
	{ icon: <FaUsers className='w-6 h-6' />, href: '#', label: 'Community' },
	{ icon: <FaUser className='w-6 h-6' />, href: '/profile', label: 'Profile' },
	{
		icon: <MdEventAvailable className='w-6 h-6' />,
		href: '/session',
		label: 'Book Session',
	},
	{
		icon: <FaCalendarDay className='w-6 h-6' />,
		href: '/bookings',
		label: 'My Bookings',
	},
];

// Sidebar Toggle Button
const SidebarToggleButton = ({ onClick, isCollapsed }) => (
	<button
		onClick={onClick}
		className='text-white p-2 hover:bg-teal-700 rounded-lg'>
		{isCollapsed ? (
			<FaBars className='w-6 h-6' />
		) : (
			<FaChevronLeft className='w-6 h-6' />
		)}
	</button>
);

const Sidebar = ({ isOpen, onToggle }) => {
	const [showSignOutModal, setShowSignOutModal] = useState(false);
	const [isCollapsed, setIsCollapsed] = useState(false);

	const {
		data: userProfile,
		isLoading: isUserLoading,
		error: userError,
	} = useUserProfile();

	const handleSignOutClick = (e) => {
		e.preventDefault();
		setShowSignOutModal(true);
	};

	const handleSignOut = () => {
		// Implement sign out logic here
		setShowSignOutModal(false);
	};

	const toggleCollapse = () => setIsCollapsed(!isCollapsed);
	const pathname = usePathname();

	return (
		<>
			<div
				className={`z-50 fixed inset-y-0 left-0 ${
					isCollapsed ? 'w-20' : 'w-64'
				} bg-teal-600 text-white transform ${
					isOpen ? 'translate-x-0' : '-translate-x-full'
				} transition-all duration-300 ease-in-out z-50 rounded-r-3xl`}>
				<div className='p-4 flex justify-between items-center'>
					<SidebarToggleButton
						onClick={toggleCollapse}
						isCollapsed={isCollapsed}
					/>
					{!isCollapsed && (
						<button
							onClick={onToggle}
							className='text-white p-2 hover:bg-teal-700 rounded-lg'>
							<FaChevronLeft className='w-5 h-5' />
						</button>
					)}
				</div>

				{isCollapsed ? (
					<nav className='mt-4 px-2'>
						<ul className='space-y-4 flex flex-col items-center'>
							{navLinks.map((link, index) => {
								const active = pathname === link.href;
								return (
									<li key={index}>
										<a
											href={link.href}
											className={`p-3 hover:bg-teal-700 rounded-lg flex justify-center ${
												active ? 'bg-teal-700' : ''
											}`}>
											{link.icon}
										</a>
									</li>
								);
							})}
						</ul>
					</nav>
				) : (
					<SidebarNav
						navLinks={navLinks}
						pathname={pathname}
					/>
				)}

				<div
					className={`absolute bottom-4 ${
						isCollapsed ? 'left-0 right-0 flex justify-center' : 'left-4'
					}`}>
					{userProfile?.user?.roles[0] === 'superadmin' && (
						<Link
							href='/admin/overview'
							className={`flex items-center ${
								isCollapsed ? 'p-3 justify-center' : 'p-4'
							} hover:bg-teal-700 rounded-lg`}>
							<MdAdminPanelSettings
								className={`w-5 h-5 ${!isCollapsed && 'mr-3'}`}
							/>
							{!isCollapsed && <span>Admin Panel</span>}
						</Link>
					)}

					<a
						href='#'
						onClick={handleSignOutClick}
						className={`flex items-center ${
							isCollapsed ? 'p-3 justify-center' : 'p-4'
						} hover:bg-teal-700 rounded-lg`}>
						<FaSignOutAlt className={`w-5 h-5 ${!isCollapsed && 'mr-3'}`} />
						{!isCollapsed && <span>Sign Out</span>}
					</a>
				</div>
			</div>

			{isOpen && (
				<div
					className='fixed inset-0 bg-black opacity-50 z-40'
					onClick={onToggle}></div>
			)}

			{showSignOutModal && (
				<SignOutConfirmModal
					onCancel={() => setShowSignOutModal(false)}
					onConfirm={handleSignOut}
				/>
			)}
		</>
	);
};

export default Sidebar;
